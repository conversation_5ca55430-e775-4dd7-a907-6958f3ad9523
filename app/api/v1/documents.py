"""
Main documents API router that combines all document-related endpoints.
This module imports and includes sub-routers from specialized modules.
"""
from fastapi import APIRouter

from app.api.v1.document_crud import router as crud_router
from app.api.v1.document_workflow import router as workflow_router
from app.api.v1.document_refinement import router as refinement_router
from app.api.v1.learning import router as learning_router

router = APIRouter()

# Include sub-routers
router.include_router(crud_router, tags=["documents-crud"])
router.include_router(workflow_router, tags=["documents-workflow"])
router.include_router(refinement_router, tags=["documents-refinement"])
router.include_router(learning_router, prefix="/learning", tags=["learning-ai"])
