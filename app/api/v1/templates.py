"""
Document template management API endpoints.
Handles CRUD operations for document templates.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from typing import List, Optional
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document
from app.models.template import DocumentTemplate, TemplateUsageLog, TemplateRating
from app.schemas.template import (
    TemplateCreate,
    TemplateUpdate,
    TemplateResponse,
    TemplateListResponse,
    TemplateListItem,
    UseTemplateRequest,
    MessageResponse,
    VisibilityLevel
)
from app.schemas.document import DocumentResponse
from app.services.anthropic_service import anthropic_service

router = APIRouter()


def get_accessible_templates_query(db: Session, user: User, tenant: Tenant, document_type: str):
    """Get query for templates accessible to the user"""
    return db.query(DocumentTemplate).filter(
        DocumentTemplate.document_type == document_type,
        DocumentTemplate.is_active == True,
        or_(
            # User's own templates
            and_(
                DocumentTemplate.user_id == user.id,
                DocumentTemplate.tenant_id == tenant.id
            ),
            # Tenant-visible templates
            and_(
                DocumentTemplate.tenant_id == tenant.id,
                DocumentTemplate.visibility == VisibilityLevel.TENANT
            ),
            # Public templates
            DocumentTemplate.visibility == VisibilityLevel.PUBLIC
        )
    )


@router.get("/{doc_type}", response_model=TemplateListResponse)
async def list_templates(
    doc_type: str,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    search: Optional[str] = Query(None, description="Search in name and description"),
    tags: Optional[str] = Query(None, description="Comma-separated tags to filter by"),
    visibility: Optional[VisibilityLevel] = Query(None, description="Filter by visibility"),
    sort_by: str = Query("created_at", description="Sort by: created_at, usage_count, name"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    List templates for document type
    """
    query = get_accessible_templates_query(db, current_user, current_tenant, doc_type)
    
    # Apply filters
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                DocumentTemplate.name.ilike(search_term),
                DocumentTemplate.description.ilike(search_term)
            )
        )
    
    if tags:
        tag_list = [tag.strip() for tag in tags.split(",")]
        for tag in tag_list:
            query = query.filter(DocumentTemplate.tags.contains([tag]))
    
    if visibility:
        query = query.filter(DocumentTemplate.visibility == visibility)
    
    # Apply sorting
    if sort_by == "usage_count":
        order_col = DocumentTemplate.usage_count
    elif sort_by == "name":
        order_col = DocumentTemplate.name
    else:
        order_col = DocumentTemplate.created_at
    
    if sort_order == "asc":
        query = query.order_by(order_col)
    else:
        query = query.order_by(desc(order_col))
    
    # Get total count
    total_count = query.count()
    
    # Apply pagination
    templates = query.offset(offset).limit(limit).all()
    
    # Convert to list items
    template_items = [
        TemplateListItem(
            id=template.id,
            name=template.name,
            description=template.description,
            document_type=template.document_type,
            template_type=template.template_type,
            visibility=template.visibility,
            tags=template.tags,
            usage_count=template.usage_count,
            created_at=template.created_at,
            last_used_at=template.last_used_at
        )
        for template in templates
    ]
    
    return TemplateListResponse(
        templates=template_items,
        total_count=total_count,
        document_type=doc_type
    )


@router.get("/{doc_type}/{template_id}", response_model=TemplateResponse)
async def get_template(
    doc_type: str,
    template_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get specific template
    """
    template = get_accessible_templates_query(db, current_user, current_tenant, doc_type).filter(
        DocumentTemplate.id == template_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Template not found"
        )
    
    return template


@router.post("/{doc_type}", response_model=TemplateResponse)
async def create_template(
    doc_type: str,
    template_data: TemplateCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Create custom template
    """
    # Validate document type matches
    if template_data.document_type != doc_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Document type in URL must match template document type"
        )
    
    # Create template
    template = DocumentTemplate(
        name=template_data.name,
        description=template_data.description,
        content=template_data.content,
        document_type=template_data.document_type,
        variables=template_data.variables,
        metadata=template_data.metadata,
        template_type="custom",
        visibility=template_data.visibility,
        tags=template_data.tags,
        user_id=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(template)
    db.commit()
    db.refresh(template)
    
    return template


@router.put("/{doc_type}/{template_id}", response_model=TemplateResponse)
async def update_template(
    doc_type: str,
    template_id: uuid.UUID,
    template_data: TemplateUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update template
    """
    # Get template (only user's own templates can be updated)
    template = db.query(DocumentTemplate).filter(
        DocumentTemplate.id == template_id,
        DocumentTemplate.document_type == doc_type,
        DocumentTemplate.user_id == current_user.id,
        DocumentTemplate.tenant_id == current_tenant.id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Template not found or not editable"
        )
    
    # Update fields
    update_data = template_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(template, field, value)
    
    template.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(template)
    
    return template


@router.delete("/{doc_type}/{template_id}", response_model=MessageResponse)
async def delete_template(
    doc_type: str,
    template_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete template
    """
    # Get template (only user's own templates can be deleted)
    template = db.query(DocumentTemplate).filter(
        DocumentTemplate.id == template_id,
        DocumentTemplate.document_type == doc_type,
        DocumentTemplate.user_id == current_user.id,
        DocumentTemplate.tenant_id == current_tenant.id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Template not found or not deletable"
        )
    
    # Soft delete by marking as inactive
    template.is_active = False
    template.updated_at = datetime.now(timezone.utc)
    db.commit()
    
    return MessageResponse(
        message="Template deleted successfully",
        details={"template_id": str(template_id)}
    )


@router.post("/{template_id}/use", response_model=DocumentResponse)
async def use_template(
    template_id: uuid.UUID,
    request: UseTemplateRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Use template to create new document
    """
    # Get template
    template = get_accessible_templates_query(db, current_user, current_tenant, "").filter(
        DocumentTemplate.id == template_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Template not found"
        )
    
    try:
        # Process template with variables
        content = template.content
        
        # Simple variable substitution (in production, use proper template engine)
        for var_name, var_value in request.variable_values.items():
            placeholder = f"{{{{{var_name}}}}}"
            content = content.replace(placeholder, str(var_value))
        
        # Generate title
        title = request.title or f"Document from {template.name}"
        
        # Create document
        document = Document(
            title=title,
            content=content,
            document_type_id=None,  # Would need to map from template.document_type
            form_data=request.variable_values,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            status="draft" if request.save_as_draft else "review"
        )
        
        db.add(document)
        
        # Log template usage
        usage_log = TemplateUsageLog(
            template_id=template.id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            document_id=document.id,
            variable_values=request.variable_values,
            success=True
        )
        
        db.add(usage_log)
        
        # Update template usage count
        template.usage_count += 1
        template.last_used_at = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(document)
        
        return document
        
    except Exception as e:
        # Log failed usage
        usage_log = TemplateUsageLog(
            template_id=template.id,
            user_id=current_user.id,
            tenant_id=current_tenant.id,
            variable_values=request.variable_values,
            success=False,
            error_message=str(e)
        )
        
        db.add(usage_log)
        db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create document from template: {str(e)}"
        )
