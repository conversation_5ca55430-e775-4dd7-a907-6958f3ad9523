"""
Template and component library related Pydantic schemas.
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from enum import Enum
import uuid


class TemplateType(str, Enum):
    """Template types"""
    SYSTEM = "system"  # Built-in templates
    CUSTOM = "custom"  # User-created templates
    SHARED = "shared"  # Shared within tenant


class ComponentType(str, Enum):
    """Component types"""
    TEXT_BLOCK = "text_block"
    TABLE = "table"
    LIST = "list"
    DIAGRAM = "diagram"
    CODE_BLOCK = "code_block"
    FORM = "form"
    SECTION = "section"
    HEADER = "header"
    FOOTER = "footer"


class VisibilityLevel(str, Enum):
    """Visibility levels for templates and components"""
    PRIVATE = "private"  # Only creator can see
    TENANT = "tenant"    # All users in tenant can see
    PUBLIC = "public"    # All users can see (system templates)


# Base schemas
class TemplateBase(BaseModel):
    """Base template schema"""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    document_type: str = Field(..., description="Document type this template is for")


class ComponentBase(BaseModel):
    """Base component schema"""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    component_type: ComponentType
    document_types: List[str] = Field(default_factory=list, description="Applicable document types")


# Template schemas
class TemplateCreate(TemplateBase):
    """Create template request"""
    content: str = Field(..., description="Template content")
    variables: List[Dict[str, Any]] = Field(default_factory=list, description="Template variables schema")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional template metadata")
    visibility: VisibilityLevel = Field(VisibilityLevel.PRIVATE, description="Template visibility")
    tags: List[str] = Field(default_factory=list, description="Template tags for categorization")


class TemplateUpdate(BaseModel):
    """Update template request"""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    content: Optional[str] = Field(None, description="Template content")
    variables: Optional[List[Dict[str, Any]]] = Field(None, description="Template variables schema")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional template metadata")
    visibility: Optional[VisibilityLevel] = Field(None, description="Template visibility")
    tags: Optional[List[str]] = Field(None, description="Template tags for categorization")
    is_active: Optional[bool] = Field(None, description="Whether template is active")


class TemplateResponse(TemplateBase):
    """Template response"""
    id: uuid.UUID
    content: str
    variables: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    template_type: TemplateType
    visibility: VisibilityLevel
    tags: List[str]
    is_active: bool
    usage_count: int
    
    # Ownership
    user_id: uuid.UUID
    tenant_id: uuid.UUID
    
    # Timestamps
    created_at: datetime
    updated_at: Optional[datetime]
    last_used_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class TemplateListItem(BaseModel):
    """Template list item (lightweight)"""
    id: uuid.UUID
    name: str
    description: Optional[str]
    document_type: str
    template_type: TemplateType
    visibility: VisibilityLevel
    tags: List[str]
    usage_count: int
    created_at: datetime
    last_used_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class TemplateListResponse(BaseModel):
    """Template list response"""
    templates: List[TemplateListItem]
    total_count: int
    document_type: str


class UseTemplateRequest(BaseModel):
    """Use template to create document request"""
    title: Optional[str] = Field(None, description="Document title override")
    variable_values: Dict[str, Any] = Field(default_factory=dict, description="Values for template variables")
    save_as_draft: bool = Field(True, description="Save as draft or generate immediately")


# Component schemas
class ComponentCreate(ComponentBase):
    """Create component request"""
    content: str = Field(..., description="Component content/template")
    variables: List[Dict[str, Any]] = Field(default_factory=list, description="Component variables")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Component metadata")
    visibility: VisibilityLevel = Field(VisibilityLevel.PRIVATE, description="Component visibility")
    tags: List[str] = Field(default_factory=list, description="Component tags")
    category: Optional[str] = Field(None, description="Component category")


class ComponentUpdate(BaseModel):
    """Update component request"""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    content: Optional[str] = Field(None, description="Component content/template")
    variables: Optional[List[Dict[str, Any]]] = Field(None, description="Component variables")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Component metadata")
    visibility: Optional[VisibilityLevel] = Field(None, description="Component visibility")
    tags: Optional[List[str]] = Field(None, description="Component tags")
    category: Optional[str] = Field(None, description="Component category")
    document_types: Optional[List[str]] = Field(None, description="Applicable document types")
    is_active: Optional[bool] = Field(None, description="Whether component is active")


class ComponentResponse(ComponentBase):
    """Component response"""
    id: uuid.UUID
    content: str
    variables: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    visibility: VisibilityLevel
    tags: List[str]
    category: Optional[str]
    is_active: bool
    usage_count: int
    
    # Ownership
    user_id: uuid.UUID
    tenant_id: uuid.UUID
    
    # Timestamps
    created_at: datetime
    updated_at: Optional[datetime]
    last_used_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class ComponentListItem(BaseModel):
    """Component list item (lightweight)"""
    id: uuid.UUID
    name: str
    description: Optional[str]
    component_type: ComponentType
    document_types: List[str]
    category: Optional[str]
    tags: List[str]
    usage_count: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class ComponentListResponse(BaseModel):
    """Component list response"""
    components: List[ComponentListItem]
    total_count: int
    document_type: Optional[str] = None
    categories: List[str] = Field(default_factory=list, description="Available categories")


# Section schemas
class SectionCreate(BaseModel):
    """Create section pattern request"""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    document_type: str = Field(..., description="Document type this section is for")
    content: str = Field(..., description="Section content template")
    variables: List[Dict[str, Any]] = Field(default_factory=list, description="Section variables")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Section metadata")
    visibility: VisibilityLevel = Field(VisibilityLevel.PRIVATE, description="Section visibility")
    tags: List[str] = Field(default_factory=list, description="Section tags")
    order_hint: int = Field(0, description="Suggested order in document")


class SectionResponse(BaseModel):
    """Section response"""
    id: uuid.UUID
    name: str
    description: Optional[str]
    document_type: str
    content: str
    variables: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    visibility: VisibilityLevel
    tags: List[str]
    order_hint: int
    usage_count: int
    
    # Ownership
    user_id: uuid.UUID
    tenant_id: uuid.UUID
    
    # Timestamps
    created_at: datetime
    updated_at: Optional[datetime]
    last_used_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class SectionListResponse(BaseModel):
    """Section list response"""
    sections: List[SectionResponse]
    total_count: int
    document_type: str


# Usage tracking schemas
class TemplateUsageStats(BaseModel):
    """Template usage statistics"""
    template_id: uuid.UUID
    total_uses: int
    recent_uses: int  # Last 30 days
    avg_rating: Optional[float]
    last_used_at: Optional[datetime]


class ComponentUsageStats(BaseModel):
    """Component usage statistics"""
    component_id: uuid.UUID
    total_uses: int
    recent_uses: int  # Last 30 days
    document_types_used: List[str]
    last_used_at: Optional[datetime]


# Generic response schemas
class MessageResponse(BaseModel):
    """Generic message response"""
    message: str
    details: Optional[Dict[str, Any]] = None
