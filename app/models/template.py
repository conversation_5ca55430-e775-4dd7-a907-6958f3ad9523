"""
Template and component library database models.
"""
from sqlalchemy import Column, String, Text, Integer, DateTime, Boolean, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class DocumentTemplate(Base):
    """Document templates"""
    __tablename__ = "document_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Template details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    content = Column(Text, nullable=False)
    document_type = Column(String, nullable=False)  # prd, api_doc, etc.
    
    # Template configuration
    variables = Column(JSON, default=[])  # Template variables schema
    metadata = Column(JSON, default={})   # Additional template metadata
    template_type = Column(String, default="custom")  # system, custom, shared
    visibility = Column(String, default="private")    # private, tenant, public
    
    # Categorization
    tags = Column(JSON, default=[])
    category = Column(String, nullable=True)
    
    # Ownership and access
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Ratings and feedback
    rating_sum = Column(Integer, default=0)
    rating_count = Column(Integer, default=0)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    usage_logs = relationship("TemplateUsageLog", backref="template")


class ComponentLibrary(Base):
    """Reusable document components"""
    __tablename__ = "component_library"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Component details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    content = Column(Text, nullable=False)
    component_type = Column(String, nullable=False)  # text_block, table, list, etc.
    
    # Applicability
    document_types = Column(JSON, default=[])  # List of applicable document types
    
    # Component configuration
    variables = Column(JSON, default=[])  # Component variables
    metadata = Column(JSON, default={})   # Component metadata
    visibility = Column(String, default="private")  # private, tenant, public
    
    # Categorization
    tags = Column(JSON, default=[])
    category = Column(String, nullable=True)
    
    # Ownership and access
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    usage_logs = relationship("ComponentUsageLog", backref="component")


class SectionLibrary(Base):
    """Common section patterns"""
    __tablename__ = "section_library"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Section details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    content = Column(Text, nullable=False)
    document_type = Column(String, nullable=False)
    
    # Section configuration
    variables = Column(JSON, default=[])  # Section variables
    metadata = Column(JSON, default={})   # Section metadata
    visibility = Column(String, default="private")  # private, tenant, public
    
    # Positioning
    order_hint = Column(Integer, default=0)  # Suggested order in document
    
    # Categorization
    tags = Column(JSON, default=[])
    category = Column(String, nullable=True)
    
    # Ownership and access
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    usage_logs = relationship("SectionUsageLog", backref="section")


class TemplateUsageLog(Base):
    """Template usage tracking"""
    __tablename__ = "template_usage_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # References
    template_id = Column(UUID(as_uuid=True), ForeignKey("document_templates.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    document_id = Column(UUID(as_uuid=True), nullable=True)  # If document was created
    
    # Usage details
    variable_values = Column(JSON, default={})  # Values used for variables
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    
    # Context
    user_agent = Column(String, nullable=True)
    ip_address = Column(String, nullable=True)
    
    # Timing
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class ComponentUsageLog(Base):
    """Component usage tracking"""
    __tablename__ = "component_usage_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # References
    component_id = Column(UUID(as_uuid=True), ForeignKey("component_library.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    document_id = Column(UUID(as_uuid=True), nullable=True)
    
    # Usage details
    document_type = Column(String, nullable=False)
    variable_values = Column(JSON, default={})
    
    # Context
    user_agent = Column(String, nullable=True)
    
    # Timing
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class SectionUsageLog(Base):
    """Section usage tracking"""
    __tablename__ = "section_usage_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # References
    section_id = Column(UUID(as_uuid=True), ForeignKey("section_library.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    document_id = Column(UUID(as_uuid=True), nullable=True)
    
    # Usage details
    document_type = Column(String, nullable=False)
    variable_values = Column(JSON, default={})
    
    # Timing
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class TemplateRating(Base):
    """Template ratings and feedback"""
    __tablename__ = "template_ratings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # References
    template_id = Column(UUID(as_uuid=True), ForeignKey("document_templates.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Rating
    rating = Column(Integer, nullable=False)  # 1-5 stars
    feedback = Column(Text, nullable=True)
    
    # Timing
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    template = relationship("DocumentTemplate", backref="ratings")


class TemplateCategory(Base):
    """Template categories for organization"""
    __tablename__ = "template_categories"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Category details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    document_type = Column(String, nullable=False)
    
    # Display
    icon = Column(String, nullable=True)
    color = Column(String, nullable=True)
    order_index = Column(Integer, default=0)
    
    # Ownership
    tenant_id = Column(UUID(as_uuid=True), nullable=True)  # Null for system categories
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ComponentCategory(Base):
    """Component categories for organization"""
    __tablename__ = "component_categories"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Category details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    component_type = Column(String, nullable=True)  # Null for all types
    
    # Display
    icon = Column(String, nullable=True)
    color = Column(String, nullable=True)
    order_index = Column(Integer, default=0)
    
    # Ownership
    tenant_id = Column(UUID(as_uuid=True), nullable=True)  # Null for system categories
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
